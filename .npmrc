# pnpm configuration
auto-install-peers=true
strict-peer-dependencies=false
prefer-workspace-packages=true
save-workspace-protocol=false
link-workspace-packages=true

# Node.js configuration
engine-strict=true

# Registry configuration
registry=https://registry.npmjs.org/

# Hoisting configuration
hoist-pattern[]=*eslint*
hoist-pattern[]=*prettier*
hoist-pattern[]=*typescript*

# Shamefully hoist some problematic packages
shamefully-hoist=true

# Store configuration
store-dir=~/.pnpm-store
